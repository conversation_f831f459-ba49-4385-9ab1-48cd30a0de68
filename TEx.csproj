﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProjectGuid>{2EB83C6E-A57A-4B6C-A7A5-E5FD05778592}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TEx</RootNamespace>
    <AssemblyName>TEx</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>TEx.ico</ApplicationIcon>
    <StartupObject>ns21.Class351</StartupObject>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutocompleteMenu-ScintillaNET">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\AutocompleteMenu-ScintillaNET.dll</HintPath>
    </Reference>
    <Reference Include="DevComponents.DotNetBar2">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\DevComponents.DotNetBar2.dll</HintPath>
    </Reference>
    <Reference Include="Interop.SHDocVw">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\Interop.SHDocVw.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.mshtml">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\Microsoft.mshtml.dll</HintPath>
    </Reference>
    <Reference Include="NAppUpdate.Framework">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\NAppUpdate.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="ScintillaNET">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\ScintillaNET.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="TEx.Chart">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\TEx.Chart.dll</HintPath>
    </Reference>
    <Reference Include="TEx.Comn">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\TEx.Comn.dll</HintPath>
    </Reference>
    <Reference Include="TEx.Inds">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\TEx.Inds.dll</HintPath>
    </Reference>
    <Reference Include="TEx.Util">
      <HintPath>..\..\..\..\..\Program Files\TEx Studio\交易练习者\TEx.Util.dll</HintPath>
    </Reference>
    <Reference Include="Vlc.DotNet.Core" />
    <Reference Include="Vlc.DotNet.Forms" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AcctSymblDT.cs" />
    <Compile Include="BackupSyncConflictTreatmt.cs" />
    <Compile Include="BackupSyncTimeInterval.cs" />
    <Compile Include="BaoDian.cs" />
    <Compile Include="BaoDianMgr.cs" />
    <Compile Include="Base.cs" />
    <Compile Include="BkupSyncCnfmWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BkupSyncCnfmWnd.Designer.cs">
      <DependentUpon>BkupSyncCnfmWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="BkupSyncMgr.cs" />
    <Compile Include="BlindTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BlindTestForm.Designer.cs">
      <DependentUpon>BlindTestForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ChartBase.cs" />
    <Compile Include="ChartCS.cs" />
    <Compile Include="ChartKLine.cs" />
    <Compile Include="ChartKLSub.cs" />
    <Compile Include="ChartPage.cs" />
    <Compile Include="ChartParam.cs" />
    <Compile Include="ChartTheme.cs" />
    <Compile Include="ChartTickM.cs" />
    <Compile Include="ChartTickV.cs" />
    <Compile Include="ChartType.cs" />
    <Compile Include="ChartUISettings.cs" />
    <Compile Include="ChtCtrl.cs" />
    <Compile Include="ChtCtrlParam.cs" />
    <Compile Include="ChtCtrlParam_KLine.cs" />
    <Compile Include="ChtCtrl_KLine.cs" />
    <Compile Include="ChtCtrl_Tick.cs" />
    <Compile Include="ComparisonOpt.cs" />
    <Compile Include="ComputerType.cs" />
    <Compile Include="ConnMgr.cs" />
    <Compile Include="DataGridViewHisTrans.cs" />
    <Compile Include="DataGridViewMkt.cs" />
    <Compile Include="DataGridViewOpenTrans.cs" />
    <Compile Include="DataMgmtForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DataMgmtForm.Designer.cs">
      <DependentUpon>DataMgmtForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DateSelectForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DateSelectForm.Designer.cs">
      <DependentUpon>DateSelectForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DrawArwLine.cs" />
    <Compile Include="DrawDegree45Dn.cs" />
    <Compile Include="DrawDegree45Up.cs" />
    <Compile Include="DrawEllipse.cs" />
    <Compile Include="DrawFibonacciExtLines.cs" />
    <Compile Include="DrawFibonacciLines.cs" />
    <Compile Include="DrawGannFan.cs" />
    <Compile Include="DrawGannLines.cs" />
    <Compile Include="DrawGoldenRatio.cs" />
    <Compile Include="DrawGrnArwDn.cs" />
    <Compile Include="DrawGrnArwLDn.cs" />
    <Compile Include="DrawGrnArwRDn.cs" />
    <Compile Include="DrawLine.cs" />
    <Compile Include="DrawLineD.cs" />
    <Compile Include="DrawLineDExt.cs" />
    <Compile Include="DrawLineDH.cs" />
    <Compile Include="DrawLineH.cs" />
    <Compile Include="DrawLineHExt.cs" />
    <Compile Include="DrawLineP.cs" />
    <Compile Include="DrawLineStyle.cs" />
    <Compile Include="DrawLineType.cs" />
    <Compile Include="DrawLineV.cs" />
    <Compile Include="DrawLineWidth.cs" />
    <Compile Include="DrawMeasureObj.cs" />
    <Compile Include="DrawMode.cs" />
    <Compile Include="DrawObj.cs" />
    <Compile Include="DrawObjParamType.cs" />
    <Compile Include="DrawParamWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DrawParamWnd.Designer.cs">
      <DependentUpon>DrawParamWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="DrawPeriodLines.cs" />
    <Compile Include="DrawRange.cs" />
    <Compile Include="DrawRatio.cs" />
    <Compile Include="DrawRedArwLUp.cs" />
    <Compile Include="DrawRedArwRUp.cs" />
    <Compile Include="DrawRedArwUp.cs" />
    <Compile Include="DrawSquare.cs" />
    <Compile Include="DrawSublineParam.cs" />
    <Compile Include="DrawText.cs" />
    <Compile Include="DrawTrendSpeed.cs" />
    <Compile Include="DTValLocation.cs" />
    <Compile Include="FilterCond.cs" />
    <Compile Include="FilterCondItem.cs" />
    <Compile Include="FnDataApiWorker.cs" />
    <Compile Include="FormUISettings.cs" />
    <Compile Include="GraphCtrlStat.cs" />
    <Compile Include="HDFileMgr.cs" />
    <Compile Include="HiLowMarkTextObj.cs" />
    <Compile Include="HisDataPeriodSet.cs" />
    <Compile Include="HisDataSet.cs" />
    <Compile Include="ImportTransForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImportTransForm.Designer.cs">
      <DependentUpon>ImportTransForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ImportTrans\AutoDownCfmmcFrequencyEnum.cs" />
    <Compile Include="ImportTrans\BindingAcct.cs" />
    <Compile Include="ImportTrans\Captcha\ConnectedComponent.cs" />
    <Compile Include="ImportTrans\Captcha\CutBigComponent.cs" />
    <Compile Include="ImportTrans\Captcha\ImageBeyes.cs" />
    <Compile Include="ImportTrans\Captcha\ImageProcessor.cs" />
    <Compile Include="ImportTrans\Captcha\LevenClassifer.cs" />
    <Compile Include="ImportTrans\Captcha\NaiveBeyes.cs" />
    <Compile Include="ImportTrans\Captcha\Reg.cs" />
    <Compile Include="ImportTrans\CCfmmcRecorderStore.cs" />
    <Compile Include="ImportTrans\CfmmcAcct.cs" />
    <Compile Include="ImportTrans\CfmmcAutoDnldConfig.cs" />
    <Compile Include="ImportTrans\CfmmcRecFieldsEnum.cs" />
    <Compile Include="ImportTrans\CfmmcRecImporter.cs" />
    <Compile Include="ImportTrans\CfmmcRecord.cs" />
    <Compile Include="ImportTrans\CfmmcWebDnloader.cs" />
    <Compile Include="ImportTrans\CWrongNameStore.cs" />
    <Compile Include="ImportTrans\CWrongUserName.cs" />
    <Compile Include="ImportTrans\EDownDay.cs" />
    <Compile Include="ImportTrans\IStoreElement.cs" />
    <Compile Include="ImportTrans\TransData.cs" />
    <Compile Include="ImportTrans\TransFileImporter.cs" />
    <Compile Include="IndCurve.cs" />
    <Compile Include="Indicator.cs" />
    <Compile Include="IndicatorEventHandler.cs" />
    <Compile Include="IndInputDataType.cs" />
    <Compile Include="IndParam.cs" />
    <Compile Include="InfoMineApiWorker.cs" />
    <Compile Include="InfoMineMgr.cs" />
    <Compile Include="KeyModifiers.cs" />
    <Compile Include="KLineType.cs" />
    <Compile Include="LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns0\Attribute4.cs" />
    <Compile Include="ns0\Class0.cs" />
    <Compile Include="ns0\Class451.cs" />
    <Compile Include="ns0\Class481.cs" />
    <Compile Include="ns0\Class54.cs" />
    <Compile Include="ns0\EventArgs23.cs" />
    <Compile Include="ns10\Class15.cs" />
    <Compile Include="ns10\Class193.cs" />
    <Compile Include="ns10\Class385.cs" />
    <Compile Include="ns10\Class416.cs" />
    <Compile Include="ns10\Class421.cs" />
    <Compile Include="ns10\Class484.cs" />
    <Compile Include="ns10\Class533.cs" />
    <Compile Include="ns10\Delegate15.cs" />
    <Compile Include="ns10\Enum7.cs" />
    <Compile Include="ns10\Interface3.cs" />
    <Compile Include="ns11\Attribute2.cs" />
    <Compile Include="ns11\Class16.cs" />
    <Compile Include="ns11\Class396.cs" />
    <Compile Include="ns11\Class442.cs" />
    <Compile Include="ns11\Class466.cs" />
    <Compile Include="ns11\Control13.cs" />
    <Compile Include="ns11\EventArgs34.cs" />
    <Compile Include="ns11\Form0.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns11\Form0.Designer.cs">
      <DependentUpon>Form0.cs</DependentUpon>
    </Compile>
    <Compile Include="ns11\Form19.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns11\Form19.Designer.cs">
      <DependentUpon>Form19.cs</DependentUpon>
    </Compile>
    <Compile Include="ns11\Form8.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns11\Form8.Designer.cs">
      <DependentUpon>Form8.cs</DependentUpon>
    </Compile>
    <Compile Include="ns12\Class17.cs" />
    <Compile Include="ns12\Class266.cs" />
    <Compile Include="ns12\Class285.cs" />
    <Compile Include="ns12\Class327.cs" />
    <Compile Include="ns12\Class359.cs" />
    <Compile Include="ns12\Class387.cs" />
    <Compile Include="ns12\Class395.cs" />
    <Compile Include="ns12\Class411.cs" />
    <Compile Include="ns12\EventArgs19.cs" />
    <Compile Include="ns12\Form3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns12\Form3.Designer.cs">
      <DependentUpon>Form3.cs</DependentUpon>
    </Compile>
    <Compile Include="ns12\Struct5.cs" />
    <Compile Include="ns13\Class186.cs" />
    <Compile Include="ns13\Class21.cs" />
    <Compile Include="ns13\Class419.cs" />
    <Compile Include="ns13\Class515.cs" />
    <Compile Include="ns13\Class60.cs" />
    <Compile Include="ns13\Control2.cs" />
    <Compile Include="ns13\Delegate38.cs" />
    <Compile Include="ns13\Enum33.cs" />
    <Compile Include="ns13\EventArgs0.cs" />
    <Compile Include="ns13\EventArgs17.cs" />
    <Compile Include="ns13\Form24.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns13\Form24.Designer.cs">
      <DependentUpon>Form24.cs</DependentUpon>
    </Compile>
    <Compile Include="ns13\Interface1.cs" />
    <Compile Include="ns14\Class180.cs" />
    <Compile Include="ns14\Class22.cs" />
    <Compile Include="ns14\Class241.cs" />
    <Compile Include="ns14\Class417.cs" />
    <Compile Include="ns14\Class423.cs" />
    <Compile Include="ns14\Class56.cs" />
    <Compile Include="ns14\Delegate12.cs" />
    <Compile Include="ns14\Enum12.cs" />
    <Compile Include="ns14\Enum2.cs" />
    <Compile Include="ns14\Form27.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns14\Form27.Designer.cs">
      <DependentUpon>Form27.cs</DependentUpon>
    </Compile>
    <Compile Include="ns15\Class23.cs" />
    <Compile Include="ns15\Class516.cs" />
    <Compile Include="ns15\Class520.cs" />
    <Compile Include="ns15\Enum20.cs" />
    <Compile Include="ns15\EventArgs15.cs" />
    <Compile Include="ns15\EventArgs24.cs" />
    <Compile Include="ns15\EventArgs5.cs" />
    <Compile Include="ns15\Form5.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns15\Form5.Designer.cs">
      <DependentUpon>Form5.cs</DependentUpon>
    </Compile>
    <Compile Include="ns16\Class24.cs" />
    <Compile Include="ns16\Class265.cs" />
    <Compile Include="ns16\Class283.cs" />
    <Compile Include="ns16\Class397.cs" />
    <Compile Include="ns16\Class440.cs" />
    <Compile Include="ns16\Class510.cs" />
    <Compile Include="ns16\Class513.cs" />
    <Compile Include="ns16\Class518.cs" />
    <Compile Include="ns16\Delegate10.cs" />
    <Compile Include="ns16\Delegate28.cs" />
    <Compile Include="ns16\Form4.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns16\Form4.Designer.cs">
      <DependentUpon>Form4.cs</DependentUpon>
    </Compile>
    <Compile Include="ns17\Attribute6.cs" />
    <Compile Include="ns17\Class182.cs" />
    <Compile Include="ns17\Class399.cs" />
    <Compile Include="ns17\Class514.cs" />
    <Compile Include="ns17\Class540.cs" />
    <Compile Include="ns17\Delegate39.cs" />
    <Compile Include="ns17\Enum0.cs" />
    <Compile Include="ns17\Enum25.cs" />
    <Compile Include="ns17\EventArgs6.cs" />
    <Compile Include="ns18\Class287.cs" />
    <Compile Include="ns18\Class47.cs" />
    <Compile Include="ns18\Class521.cs" />
    <Compile Include="ns18\Class69.cs" />
    <Compile Include="ns18\Delegate3.cs" />
    <Compile Include="ns18\Enum16.cs" />
    <Compile Include="ns18\EventArgs14.cs" />
    <Compile Include="ns18\Exception0.cs" />
    <Compile Include="ns18\Form10.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns18\Form10.Designer.cs">
      <DependentUpon>Form10.cs</DependentUpon>
    </Compile>
    <Compile Include="ns18\Struct3.cs" />
    <Compile Include="ns19\Class350.cs" />
    <Compile Include="ns19\Delegate0.cs" />
    <Compile Include="ns19\Delegate30.cs" />
    <Compile Include="ns19\EventArgs16.cs" />
    <Compile Include="ns19\Interface0.cs" />
    <Compile Include="ns1\Class1.cs" />
    <Compile Include="ns1\Class226.cs" />
    <Compile Include="ns1\Class296.cs" />
    <Compile Include="ns1\Class303.cs" />
    <Compile Include="ns1\Class386.cs" />
    <Compile Include="ns1\Class473.cs" />
    <Compile Include="ns1\Class536.cs" />
    <Compile Include="ns1\Delegate37.cs" />
    <Compile Include="ns1\EventArgs12.cs" />
    <Compile Include="ns1\EventArgs28.cs" />
    <Compile Include="ns1\Form6.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns1\Form6.Designer.cs">
      <DependentUpon>Form6.cs</DependentUpon>
    </Compile>
    <Compile Include="ns20\Class299.cs" />
    <Compile Include="ns20\Class394.cs" />
    <Compile Include="ns20\Class420.cs" />
    <Compile Include="ns20\Class48.cs" />
    <Compile Include="ns20\Class491.cs" />
    <Compile Include="ns20\Class534.cs" />
    <Compile Include="ns20\Class66.cs" />
    <Compile Include="ns20\Delegate25.cs" />
    <Compile Include="ns20\Delegate29.cs" />
    <Compile Include="ns20\Delegate8.cs" />
    <Compile Include="ns20\Enum1.cs" />
    <Compile Include="ns20\Enum32.cs" />
    <Compile Include="ns20\Enum34.cs" />
    <Compile Include="ns20\EventArgs4.cs" />
    <Compile Include="ns20\Form16.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns20\Form16.Designer.cs">
      <DependentUpon>Form16.cs</DependentUpon>
    </Compile>
    <Compile Include="ns20\Form20.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns20\Form20.Designer.cs">
      <DependentUpon>Form20.cs</DependentUpon>
    </Compile>
    <Compile Include="ns20\Form7.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns20\Form7.Designer.cs">
      <DependentUpon>Form7.cs</DependentUpon>
    </Compile>
    <Compile Include="ns21\Attribute5.cs" />
    <Compile Include="ns21\Class185.cs" />
    <Compile Include="ns21\Class289.cs" />
    <Compile Include="ns21\Class302.cs" />
    <Compile Include="ns21\Class351.cs" />
    <Compile Include="ns21\Class401.cs" />
    <Compile Include="ns21\Class424.cs" />
    <Compile Include="ns21\Class545.cs" />
    <Compile Include="ns21\Control15.cs" />
    <Compile Include="ns21\Delegate5.cs" />
    <Compile Include="ns21\Enum3.cs" />
    <Compile Include="ns21\EventArgs32.cs" />
    <Compile Include="ns21\Form2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns21\Form2.Designer.cs">
      <DependentUpon>Form2.cs</DependentUpon>
    </Compile>
    <Compile Include="ns22\Attribute7.cs" />
    <Compile Include="ns22\Class210.cs" />
    <Compile Include="ns22\Class389.cs" />
    <Compile Include="ns22\Class390.cs" />
    <Compile Include="ns22\Class402.cs" />
    <Compile Include="ns22\Class517.cs" />
    <Compile Include="ns22\Class548.cs" />
    <Compile Include="ns22\Class551.cs" />
    <Compile Include="ns22\Enum36.cs" />
    <Compile Include="ns22\Enum4.cs" />
    <Compile Include="ns22\Form14.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns22\Form14.Designer.cs">
      <DependentUpon>Form14.cs</DependentUpon>
    </Compile>
    <Compile Include="ns23\Class183.cs" />
    <Compile Include="ns23\Class293.cs" />
    <Compile Include="ns23\Class443.cs" />
    <Compile Include="ns23\Class547.cs" />
    <Compile Include="ns23\Control12.cs" />
    <Compile Include="ns23\Enum6.cs" />
    <Compile Include="ns23\Enum8.cs" />
    <Compile Include="ns23\EventArgs13.cs" />
    <Compile Include="ns24\Class388.cs" />
    <Compile Include="ns24\Class509.cs" />
    <Compile Include="ns24\Class61.cs" />
    <Compile Include="ns24\Control6.cs" />
    <Compile Include="ns24\Delegate14.cs" />
    <Compile Include="ns24\Enum17.cs" />
    <Compile Include="ns24\EventArgs2.cs" />
    <Compile Include="ns24\EventArgs29.cs" />
    <Compile Include="ns25\Class292.cs" />
    <Compile Include="ns25\Class398.cs" />
    <Compile Include="ns25\Class486.cs" />
    <Compile Include="ns25\Class497.cs" />
    <Compile Include="ns25\Class51.cs" />
    <Compile Include="ns25\Delegate1.cs" />
    <Compile Include="ns25\Delegate20.cs" />
    <Compile Include="ns25\Delegate24.cs" />
    <Compile Include="ns25\Delegate33.cs" />
    <Compile Include="ns25\Form18.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns25\Form18.Designer.cs">
      <DependentUpon>Form18.cs</DependentUpon>
    </Compile>
    <Compile Include="ns25\Form23.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns25\Form23.Designer.cs">
      <DependentUpon>Form23.cs</DependentUpon>
    </Compile>
    <Compile Include="ns25\Form25.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns25\Form25.Designer.cs">
      <DependentUpon>Form25.cs</DependentUpon>
    </Compile>
    <Compile Include="ns26\Attribute3.cs" />
    <Compile Include="ns26\Class184.cs" />
    <Compile Include="ns26\Class294.cs" />
    <Compile Include="ns26\Class310.cs" />
    <Compile Include="ns26\Class375.cs" />
    <Compile Include="ns26\Class412.cs" />
    <Compile Include="ns26\Class541.cs" />
    <Compile Include="ns26\Class57.cs" />
    <Compile Include="ns26\Enum35.cs" />
    <Compile Include="ns26\EventArgs21.cs" />
    <Compile Include="ns26\EventArgs27.cs" />
    <Compile Include="ns26\Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns26\Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="ns26\Form12.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns26\Form12.Designer.cs">
      <DependentUpon>Form12.cs</DependentUpon>
    </Compile>
    <Compile Include="ns27\Class225.cs" />
    <Compile Include="ns27\Class291.cs" />
    <Compile Include="ns27\Class543.cs" />
    <Compile Include="ns27\Class67.cs" />
    <Compile Include="ns27\Control0.cs" />
    <Compile Include="ns27\Control5.cs" />
    <Compile Include="ns27\Enum11.cs" />
    <Compile Include="ns27\EventArgs22.cs" />
    <Compile Include="ns28\Class284.cs" />
    <Compile Include="ns28\Class415.cs" />
    <Compile Include="ns28\Class422.cs" />
    <Compile Include="ns28\Class522.cs" />
    <Compile Include="ns28\Class544.cs" />
    <Compile Include="ns28\Class549.cs" />
    <Compile Include="ns28\Class70.cs" />
    <Compile Include="ns28\Control16.cs" />
    <Compile Include="ns28\Delegate13.cs" />
    <Compile Include="ns28\Delegate27.cs" />
    <Compile Include="ns28\Enum18.cs" />
    <Compile Include="ns28\EventArgs26.cs" />
    <Compile Include="ns28\Interface4.cs" />
    <Compile Include="ns29\Class413.cs" />
    <Compile Include="ns29\Control17.cs" />
    <Compile Include="ns29\Delegate18.cs" />
    <Compile Include="ns29\Delegate6.cs" />
    <Compile Include="ns29\EventArgs35.cs" />
    <Compile Include="ns29\Form17.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns29\Form17.Designer.cs">
      <DependentUpon>Form17.cs</DependentUpon>
    </Compile>
    <Compile Include="ns2\Class19.cs" />
    <Compile Include="ns2\Class2.cs" />
    <Compile Include="ns2\Class453.cs" />
    <Compile Include="ns2\Class483.cs" />
    <Compile Include="ns2\Class485.cs" />
    <Compile Include="ns2\Control7.cs" />
    <Compile Include="ns2\Enum13.cs" />
    <Compile Include="ns2\Enum14.cs" />
    <Compile Include="ns2\Enum5.cs" />
    <Compile Include="ns2\EventArgs11.cs" />
    <Compile Include="ns2\EventArgs18.cs" />
    <Compile Include="ns30\Attribute1.cs" />
    <Compile Include="ns30\Class194.cs" />
    <Compile Include="ns30\Class280.cs" />
    <Compile Include="ns30\Class304.cs" />
    <Compile Include="ns30\Class546.cs" />
    <Compile Include="ns30\Struct4.cs" />
    <Compile Include="ns31\Class298.cs" />
    <Compile Include="ns31\Class391.cs" />
    <Compile Include="ns31\Class400.cs" />
    <Compile Include="ns31\Delegate21.cs" />
    <Compile Include="ns31\Delegate22.cs" />
    <Compile Include="ns31\EventArgs20.cs" />
    <Compile Include="ns31\EventArgs33.cs" />
    <Compile Include="ns31\Form11.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns31\Form11.Designer.cs">
      <DependentUpon>Form11.cs</DependentUpon>
    </Compile>
    <Compile Include="ns31\Form9.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns31\Form9.Designer.cs">
      <DependentUpon>Form9.cs</DependentUpon>
    </Compile>
    <Compile Include="ns31\Interface2.cs" />
    <Compile Include="ns32\Class519.cs" />
    <Compile Include="ns3\Attribute8.cs" />
    <Compile Include="ns3\Class288.cs" />
    <Compile Include="ns3\Class3.cs" />
    <Compile Include="ns3\Class384.cs" />
    <Compile Include="ns3\Class46.cs" />
    <Compile Include="ns3\Class469.cs" />
    <Compile Include="ns3\Class474.cs" />
    <Compile Include="ns3\Class512.cs" />
    <Compile Include="ns3\EventArgs9.cs" />
    <Compile Include="ns3\Form13.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns3\Form13.Designer.cs">
      <DependentUpon>Form13.cs</DependentUpon>
    </Compile>
    <Compile Include="ns4\Class181.cs" />
    <Compile Include="ns4\Class20.cs" />
    <Compile Include="ns4\Class308.cs" />
    <Compile Include="ns4\Class4.cs" />
    <Compile Include="ns4\Control10.cs" />
    <Compile Include="ns4\EventArgs37.cs" />
    <Compile Include="ns4\Form15.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns4\Form15.Designer.cs">
      <DependentUpon>Form15.cs</DependentUpon>
    </Compile>
    <Compile Include="ns5\Class392.cs" />
    <Compile Include="ns5\Class393.cs" />
    <Compile Include="ns5\Class43.cs" />
    <Compile Include="ns5\Class444.cs" />
    <Compile Include="ns5\Class5.cs" />
    <Compile Include="ns5\Class53.cs" />
    <Compile Include="ns5\Class550.cs" />
    <Compile Include="ns5\Class552.cs" />
    <Compile Include="ns5\Enum15.cs" />
    <Compile Include="ns6\Class11.cs" />
    <Compile Include="ns6\Class326.cs" />
    <Compile Include="ns6\Class542.cs" />
    <Compile Include="ns6\Class6.cs" />
    <Compile Include="ns6\Delegate17.cs" />
    <Compile Include="ns6\Delegate26.cs" />
    <Compile Include="ns6\Enum30.cs" />
    <Compile Include="ns6\EventArgs8.cs" />
    <Compile Include="ns6\Form26.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns6\Form26.Designer.cs">
      <DependentUpon>Form26.cs</DependentUpon>
    </Compile>
    <Compile Include="ns7\Class403.cs" />
    <Compile Include="ns7\Class52.cs" />
    <Compile Include="ns7\Class58.cs" />
    <Compile Include="ns7\Class68.cs" />
    <Compile Include="ns7\Class7.cs" />
    <Compile Include="ns7\Control1.cs" />
    <Compile Include="ns7\Control11.cs" />
    <Compile Include="ns7\Control4.cs" />
    <Compile Include="ns7\Delegate16.cs" />
    <Compile Include="ns7\Delegate4.cs" />
    <Compile Include="ns7\Enum26.cs" />
    <Compile Include="ns7\EventArgs10.cs" />
    <Compile Include="ns7\EventArgs25.cs" />
    <Compile Include="ns7\EventArgs3.cs" />
    <Compile Include="ns7\Form21.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns7\Form21.Designer.cs">
      <DependentUpon>Form21.cs</DependentUpon>
    </Compile>
    <Compile Include="ns8\Attribute0.cs" />
    <Compile Include="ns8\Class10.cs" />
    <Compile Include="ns8\Class195.cs" />
    <Compile Include="ns8\Class470.cs" />
    <Compile Include="ns8\Class482.cs" />
    <Compile Include="ns8\Class8.cs" />
    <Compile Include="ns8\Control14.cs" />
    <Compile Include="ns8\Delegate19.cs" />
    <Compile Include="ns8\Enum28.cs" />
    <Compile Include="ns8\EventArgs30.cs" />
    <Compile Include="ns9\Class14.cs" />
    <Compile Include="ns9\Class286.cs" />
    <Compile Include="ns9\Class339.cs" />
    <Compile Include="ns9\Class383.cs" />
    <Compile Include="ns9\Class414.cs" />
    <Compile Include="ns9\Class418.cs" />
    <Compile Include="ns9\Class425.cs" />
    <Compile Include="ns9\Class426.cs" />
    <Compile Include="ns9\Class452.cs" />
    <Compile Include="ns9\Class50.cs" />
    <Compile Include="ns9\Class511.cs" />
    <Compile Include="ns9\Class535.cs" />
    <Compile Include="ns9\Class55.cs" />
    <Compile Include="ns9\Class65.cs" />
    <Compile Include="ns9\Control3.cs" />
    <Compile Include="ns9\Control9.cs" />
    <Compile Include="ns9\Delegate2.cs" />
    <Compile Include="ns9\Delegate23.cs" />
    <Compile Include="ns9\Delegate9.cs" />
    <Compile Include="ns9\Enum31.cs" />
    <Compile Include="ns9\EventArgs1.cs" />
    <Compile Include="ns9\EventArgs31.cs" />
    <Compile Include="ns9\EventArgs36.cs" />
    <Compile Include="ns9\EventArgs7.cs" />
    <Compile Include="ns9\Form22.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns9\Form22.Designer.cs">
      <DependentUpon>Form22.cs</DependentUpon>
    </Compile>
    <Compile Include="ns9\Form28.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns9\Form28.Designer.cs">
      <DependentUpon>Form28.cs</DependentUpon>
    </Compile>
    <Compile Include="OrderStatus.cs" />
    <Compile Include="OrderType.cs" />
    <Compile Include="PageSelWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageSelWnd.Designer.cs">
      <DependentUpon>PageSelWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="PageUISettingCancelEventArgs.cs" />
    <Compile Include="PageUISettingEventHandler.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <DependentUpon>Settings.settings</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="QuickWndItem.cs" />
    <Compile Include="RangeSlider.cs" />
    <Compile Include="RationedShareTreatmt.cs" />
    <Compile Include="ScrShotPictureBox.cs" />
    <Compile Include="SetStopLimitForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SetStopLimitForm.Designer.cs">
      <DependentUpon>SetStopLimitForm.cs</DependentUpon>
    </Compile>
    <Compile Include="SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SettingsForm.Designer.cs">
      <DependentUpon>SettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ShowMktSymb.cs" />
    <Compile Include="SIndicator\FormIndEditer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SIndicator\FormIndEditer.Designer.cs">
      <DependentUpon>FormIndEditer.cs</DependentUpon>
    </Compile>
    <Compile Include="SIndicator\FormIndFunction.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SIndicator\FormIndFunction.Designer.cs">
      <DependentUpon>FormIndFunction.cs</DependentUpon>
    </Compile>
    <Compile Include="SIndicator\FormIndMgr.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SIndicator\FormIndMgr.Designer.cs">
      <DependentUpon>FormIndMgr.cs</DependentUpon>
    </Compile>
    <Compile Include="SIndicator\HToken.cs" />
    <Compile Include="SIndicator\IndEx.cs" />
    <Compile Include="SIndicator\ParserEnvironment.cs" />
    <Compile Include="SIndicator\ShapeCurve.cs" />
    <Compile Include="SIndicator\ShapeDrawICON.cs" />
    <Compile Include="SIndicator\Tokenes.cs" />
    <Compile Include="SIndicator\TreeFunction.cs" />
    <Compile Include="SIndicator\TreeSentence.cs" />
    <Compile Include="SIndicator\UserDefineFileMgr.cs" />
    <Compile Include="SIndicator\UserDefineInd.cs" />
    <Compile Include="SIndicator\UserDefineIndGroup.cs" />
    <Compile Include="SIndicator\UserDefineIndScript.cs" />
    <Compile Include="SmartAssembly\Shared\ReportHelper\OsInformation.cs" />
    <Compile Include="SmartAssembly\Shared\ReportHelper\OsVersionInformation.cs" />
    <Compile Include="SmartAssembly\SmartExceptionsCore\ReportingService.cs" />
    <Compile Include="SmartAssembly\SmartExceptionsCore\SmartStackFrame.cs" />
    <Compile Include="SmartAssembly\SmartExceptionsCore\UploadReportLoginService.cs" />
    <Compile Include="SplitContainerParam.cs" />
    <Compile Include="SrvParams.cs" />
    <Compile Include="StartEndDT.cs" />
    <Compile Include="StkBegEndDate.cs" />
    <Compile Include="StkSymbol.cs" />
    <Compile Include="StockRestorationMethod.cs" />
    <Compile Include="StSplit.cs" />
    <Compile Include="SymbDataSet.cs" />
    <Compile Include="SymbFilterApiWorker.cs" />
    <Compile Include="SymbFilterPanel.cs" />
    <Compile Include="SymbMgr.cs" />
    <Compile Include="SyncParam.cs" />
    <Compile Include="TApp.cs" />
    <Compile Include="TimeUnit.cs" />
    <Compile Include="TOdrLine.cs" />
    <Compile Include="Trading\Account.cs" />
    <Compile Include="Trading\AcctSymbol.cs" />
    <Compile Include="Trading\CondOrder.cs" />
    <Compile Include="Trading\Order.cs" />
    <Compile Include="Trading\ShownCondOrder.cs" />
    <Compile Include="Trading\ShownHisTrans.cs" />
    <Compile Include="Trading\ShownOpenTrans.cs" />
    <Compile Include="Trading\ShownOrder.cs" />
    <Compile Include="Trading\ShownSLOrder.cs" />
    <Compile Include="Trading\SymbolProfit.cs" />
    <Compile Include="Trading\Transaction.cs" />
    <Compile Include="Trading\TranStock.cs" />
    <Compile Include="TransArrow.cs" />
    <Compile Include="TransArrowType.cs" />
    <Compile Include="TransTabCtrl.cs" />
    <Compile Include="TransTabCtrlParam.cs" />
    <Compile Include="TransTabs.cs" />
    <Compile Include="TrdAnalysisPanel.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="BkupSyncCnfmWnd.resources" />
    <EmbeddedResource Include="BlindTestForm.resources" />
    <EmbeddedResource Include="DataMgmtForm.resources" />
    <EmbeddedResource Include="DateSelectForm.resources" />
    <EmbeddedResource Include="DrawParamWnd.resources" />
    <EmbeddedResource Include="FilterCondItem.resources" />
    <EmbeddedResource Include="ImportTransForm.resources" />
    <EmbeddedResource Include="LoginForm.resources" />
    <EmbeddedResource Include="MainForm.resources" />
    <EmbeddedResource Include="ns11\Control13.resources" />
    <EmbeddedResource Include="ns11\Form0.resources" />
    <EmbeddedResource Include="ns11\Form19.resources" />
    <EmbeddedResource Include="ns11\Form8.resources" />
    <EmbeddedResource Include="ns12\Form3.resources" />
    <EmbeddedResource Include="ns13\Control2.resources" />
    <EmbeddedResource Include="ns13\Form24.resources" />
    <EmbeddedResource Include="ns14\Form27.resources" />
    <EmbeddedResource Include="ns15\Form5.resources" />
    <EmbeddedResource Include="ns16\Form4.resources" />
    <EmbeddedResource Include="ns18\Form10.resources" />
    <EmbeddedResource Include="ns1\Form6.resources" />
    <EmbeddedResource Include="ns20\Class491.resources" />
    <EmbeddedResource Include="ns20\Form16.resources" />
    <EmbeddedResource Include="ns20\Form20.resources" />
    <EmbeddedResource Include="ns20\Form7.resources" />
    <EmbeddedResource Include="ns21\Form2.resources" />
    <EmbeddedResource Include="ns22\Form14.resources" />
    <EmbeddedResource Include="ns23\Control12.resources" />
    <EmbeddedResource Include="ns24\Control6.resources" />
    <EmbeddedResource Include="ns25\Form18.resources" />
    <EmbeddedResource Include="ns25\Form23.resources" />
    <EmbeddedResource Include="ns25\Form25.resources" />
    <EmbeddedResource Include="ns26\Class375.resources" />
    <EmbeddedResource Include="ns26\Form12.resources" />
    <EmbeddedResource Include="ns27\Control5.resources" />
    <EmbeddedResource Include="ns29\Form17.resources" />
    <EmbeddedResource Include="ns2\Control7.resources" />
    <EmbeddedResource Include="ns31\Form11.resources" />
    <EmbeddedResource Include="ns31\Form9.resources" />
    <EmbeddedResource Include="ns3\Form13.resources" />
    <EmbeddedResource Include="ns4\Control10.resources" />
    <EmbeddedResource Include="ns4\Form15.resources" />
    <EmbeddedResource Include="ns5\Class552.resources" />
    <EmbeddedResource Include="ns6\Form26.resources" />
    <EmbeddedResource Include="ns7\Control1.resources" />
    <EmbeddedResource Include="ns7\Control4.resources" />
    <EmbeddedResource Include="ns7\Form21.resources" />
    <EmbeddedResource Include="ns9\Control3.resources" />
    <EmbeddedResource Include="ns9\Control9.resources" />
    <EmbeddedResource Include="ns9\Form22.resources" />
    <EmbeddedResource Include="ns9\Form28.resources" />
    <EmbeddedResource Include="PageSelWnd.resources" />
    <EmbeddedResource Include="Resources\ICON\1.png" />
    <EmbeddedResource Include="Resources\ICON\10.png" />
    <EmbeddedResource Include="Resources\ICON\11.png" />
    <EmbeddedResource Include="Resources\ICON\12.png" />
    <EmbeddedResource Include="Resources\ICON\13.png" />
    <EmbeddedResource Include="Resources\ICON\14.png" />
    <EmbeddedResource Include="Resources\ICON\15.png" />
    <EmbeddedResource Include="Resources\ICON\16.png" />
    <EmbeddedResource Include="Resources\ICON\17.png" />
    <EmbeddedResource Include="Resources\ICON\18.png" />
    <EmbeddedResource Include="Resources\ICON\19.png" />
    <EmbeddedResource Include="Resources\ICON\2.png" />
    <EmbeddedResource Include="Resources\ICON\20.png" />
    <EmbeddedResource Include="Resources\ICON\21.png" />
    <EmbeddedResource Include="Resources\ICON\22.png" />
    <EmbeddedResource Include="Resources\ICON\23.png" />
    <EmbeddedResource Include="Resources\ICON\24.png" />
    <EmbeddedResource Include="Resources\ICON\25.png" />
    <EmbeddedResource Include="Resources\ICON\26.png" />
    <EmbeddedResource Include="Resources\ICON\27.png" />
    <EmbeddedResource Include="Resources\ICON\28.png" />
    <EmbeddedResource Include="Resources\ICON\29.png" />
    <EmbeddedResource Include="Resources\ICON\3.png" />
    <EmbeddedResource Include="Resources\ICON\30.png" />
    <EmbeddedResource Include="Resources\ICON\31.png" />
    <EmbeddedResource Include="Resources\ICON\32.png" />
    <EmbeddedResource Include="Resources\ICON\33.png" />
    <EmbeddedResource Include="Resources\ICON\34.png" />
    <EmbeddedResource Include="Resources\ICON\35.png" />
    <EmbeddedResource Include="Resources\ICON\36.png" />
    <EmbeddedResource Include="Resources\ICON\37.png" />
    <EmbeddedResource Include="Resources\ICON\38.png" />
    <EmbeddedResource Include="Resources\ICON\39.png" />
    <EmbeddedResource Include="Resources\ICON\4.png" />
    <EmbeddedResource Include="Resources\ICON\40.png" />
    <EmbeddedResource Include="Resources\ICON\41.png" />
    <EmbeddedResource Include="Resources\ICON\42.png" />
    <EmbeddedResource Include="Resources\ICON\43.png" />
    <EmbeddedResource Include="Resources\ICON\44.png" />
    <EmbeddedResource Include="Resources\ICON\45.png" />
    <EmbeddedResource Include="Resources\ICON\46.png" />
    <EmbeddedResource Include="Resources\ICON\47.png" />
    <EmbeddedResource Include="Resources\ICON\48.png" />
    <EmbeddedResource Include="Resources\ICON\49.png" />
    <EmbeddedResource Include="Resources\ICON\5.png" />
    <EmbeddedResource Include="Resources\ICON\50.png" />
    <EmbeddedResource Include="Resources\ICON\51.png" />
    <EmbeddedResource Include="Resources\ICON\6.png" />
    <EmbeddedResource Include="Resources\ICON\7.png" />
    <EmbeddedResource Include="Resources\ICON\8.png" />
    <EmbeddedResource Include="Resources\ICON\9.png" />
    <EmbeddedResource Include="SetStopLimitForm.resources" />
    <EmbeddedResource Include="SettingsForm.resources" />
    <EmbeddedResource Include="SIndicator\FormIndEditer.resources" />
    <EmbeddedResource Include="SIndicator\FormIndFunction.resources" />
    <EmbeddedResource Include="SIndicator\FormIndMgr.resources" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\current.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\data.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\default.ico" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\error.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\error16.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\network.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\ok.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\warning16.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\{logo}.png" />
    <EmbeddedResource Include="SymbFilterPanel.resources" />
    <EmbeddedResource Include="TestForm.resources" />
    <EmbeddedResource Include="TransTabCtrl.resources" />
    <EmbeddedResource Include="TransTabs.resources" />
    <EmbeddedResource Include="TrdAnalysisPanel.resources" />
    <EmbeddedResource Include="{c01b6ddb-68ca-48e0-87b3-8499e5b3ee30}" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>